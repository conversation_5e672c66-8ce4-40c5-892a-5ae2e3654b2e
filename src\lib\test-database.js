// Test database operations
import { supabase } from './supabase';
import { User, Question, UserAnswer, UserAttemptedQuestion } from '@/entities/all';

export async function testDatabaseOperations() {
  console.log('🧪 Testing database operations...');
  
  try {
    // Test 1: Connection
    console.log('1. Testing connection...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    if (connectionError) {
      console.error('❌ Connection failed:', connectionError);
      return false;
    }
    console.log('✅ Connection successful');

    // Test 2: User creation
    console.log('2. Testing user operations...');
    const currentUser = await User.me();
    console.log('✅ Current user:', currentUser);

    // Test 3: Questions
    console.log('3. Testing questions...');
    const questions = await Question.list();
    console.log('✅ Questions loaded:', questions.length);

    // Test 4: User update
    console.log('4. Testing user update...');
    const updateResult = await User.updateMyUserData({
      full_name: 'Test User ' + Date.now()
    });
    console.log('✅ User update result:', updateResult);

    // Test 5: User answers
    console.log('5. Testing user answers...');
    const userAnswers = await UserAnswer.findByUser(currentUser.id);
    console.log('✅ User answers:', userAnswers.length);

    // Test 6: User attempted questions (new junction table)
    console.log('6. Testing user attempted questions...');
    const attemptedQuestionIds = await UserAttemptedQuestion.getAttemptedQuestionIds(currentUser.id);
    console.log('✅ Attempted question IDs:', attemptedQuestionIds);

    // Test checking if a specific question was attempted (O(log n) performance)
    if (questions.length > 0) {
      const firstQuestionId = questions[0].id;
      const hasAttempted = await UserAttemptedQuestion.hasUserAttemptedQuestion(currentUser.id, firstQuestionId);
      console.log(`✅ Has attempted question ${firstQuestionId}:`, hasAttempted);

      // Test recording a new attempted question if not already attempted
      if (!hasAttempted) {
        console.log('7. Testing record attempted question...');
        const recordResult = await UserAttemptedQuestion.recordAttemptedQuestion(
          currentUser.id,
          firstQuestionId,
          true, // is_correct
          1     // points_earned
        );
        console.log('✅ Record attempted question result:', recordResult);

        // Verify it was recorded
        const hasAttemptedAfter = await UserAttemptedQuestion.hasUserAttemptedQuestion(currentUser.id, firstQuestionId);
        console.log(`✅ Has attempted question ${firstQuestionId} after recording:`, hasAttemptedAfter);
      }
    }

    // Test 8: User statistics
    console.log('8. Testing user statistics...');
    const userStats = await UserAttemptedQuestion.getUserStats(currentUser.id);
    console.log('✅ User attempt statistics:', userStats);

    console.log('🎉 All database tests passed!');
    return true;
  } catch (error) {
    console.error('❌ Database test failed:', error);
    return false;
  }
}

// Function to run tests from browser console
window.testDatabase = testDatabaseOperations;
