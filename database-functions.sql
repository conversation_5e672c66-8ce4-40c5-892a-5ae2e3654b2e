-- Optimized Database Functions for User Attempted Questions
-- These functions provide O(log n) performance for common operations

-- Function to check if a user has attempted a specific question
-- Uses EXISTS for optimal performance instead of counting rows
CREATE OR REPLACE FUNCTION check_user_attempted_question(
    p_user_id UUID,
    p_question_id INTEGER
) RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM user_attempted_questions 
        WHERE user_id = p_user_id 
        AND question_id = p_question_id
    );
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to get comprehensive user attempt statistics
-- Aggregates data efficiently in a single query
CREATE OR REPLACE FUNCTION get_user_attempt_stats(
    p_user_id UUID
) RETURNS TABLE (
    total_attempted BIGINT,
    total_correct BIGINT,
    total_points BIGINT,
    accuracy_rate NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_attempted,
        COUNT(*) FILTER (WHERE is_correct = true) as total_correct,
        COALESCE(SUM(points_earned), 0) as total_points,
        CASE 
            WHEN COUNT(*) > 0 THEN 
                ROUND(
                    (COUNT(*) FILTER (WHERE is_correct = true)::NUMERIC / COUNT(*)::NUMERIC) * 100, 
                    2
                )
            ELSE 0 
        END as accuracy_rate
    FROM user_attempted_questions
    WHERE user_id = p_user_id;
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to get questions that a user hasn't attempted yet
-- Useful for recommendation systems and filtering
CREATE OR REPLACE FUNCTION get_unattempted_questions(
    p_user_id UUID,
    p_category VARCHAR DEFAULT NULL,
    p_difficulty VARCHAR DEFAULT NULL,
    p_limit INTEGER DEFAULT 10
) RETURNS TABLE (
    id INTEGER,
    title TEXT,
    category VARCHAR,
    difficulty VARCHAR,
    options JSONB,
    correct_answer INTEGER,
    explanation TEXT,
    is_hot BOOLEAN,
    hot_date DATE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        q.id,
        q.title,
        q.category,
        q.difficulty,
        q.options,
        q.correct_answer,
        q.explanation,
        q.is_hot,
        q.hot_date
    FROM questions q
    WHERE NOT EXISTS (
        SELECT 1 
        FROM user_attempted_questions uaq 
        WHERE uaq.user_id = p_user_id 
        AND uaq.question_id = q.id
    )
    AND (p_category IS NULL OR q.category = p_category)
    AND (p_difficulty IS NULL OR q.difficulty = p_difficulty)
    ORDER BY 
        CASE WHEN q.is_hot THEN 0 ELSE 1 END,  -- Hot questions first
        q.created_at DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to get user's recent attempt history with question details
-- Useful for showing user progress and review
CREATE OR REPLACE FUNCTION get_user_attempt_history(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 20
) RETURNS TABLE (
    question_id INTEGER,
    question_title TEXT,
    category VARCHAR,
    difficulty VARCHAR,
    is_correct BOOLEAN,
    points_earned INTEGER,
    attempted_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        uaq.question_id,
        q.title as question_title,
        q.category,
        q.difficulty,
        uaq.is_correct,
        uaq.points_earned,
        uaq.attempted_at
    FROM user_attempted_questions uaq
    JOIN questions q ON q.id = uaq.question_id
    WHERE uaq.user_id = p_user_id
    ORDER BY uaq.attempted_at DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql STABLE;

-- Function to get leaderboard with attempt-based metrics
-- More comprehensive than just points-based ranking
CREATE OR REPLACE FUNCTION get_leaderboard(
    p_limit INTEGER DEFAULT 10
) RETURNS TABLE (
    user_id UUID,
    nickname VARCHAR,
    total_points BIGINT,
    total_attempted BIGINT,
    total_correct BIGINT,
    accuracy_rate NUMERIC,
    rank_position BIGINT
) AS $$
BEGIN
    RETURN QUERY
    WITH user_stats AS (
        SELECT 
            u.id as user_id,
            u.nickname,
            u.points as total_points,
            COUNT(uaq.id) as total_attempted,
            COUNT(uaq.id) FILTER (WHERE uaq.is_correct = true) as total_correct,
            CASE 
                WHEN COUNT(uaq.id) > 0 THEN 
                    ROUND(
                        (COUNT(uaq.id) FILTER (WHERE uaq.is_correct = true)::NUMERIC / COUNT(uaq.id)::NUMERIC) * 100, 
                        2
                    )
                ELSE 0 
            END as accuracy_rate
        FROM users u
        LEFT JOIN user_attempted_questions uaq ON u.id = uaq.user_id
        GROUP BY u.id, u.nickname, u.points
    )
    SELECT 
        us.user_id,
        us.nickname,
        us.total_points,
        us.total_attempted,
        us.total_correct,
        us.accuracy_rate,
        ROW_NUMBER() OVER (ORDER BY us.total_points DESC, us.accuracy_rate DESC) as rank_position
    FROM user_stats us
    ORDER BY us.total_points DESC, us.accuracy_rate DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql STABLE;

-- Indexes for optimal performance
-- These ensure O(log n) performance for the above functions

-- Composite index for the most common query pattern
CREATE INDEX IF NOT EXISTS idx_user_attempted_questions_user_question 
ON user_attempted_questions(user_id, question_id);

-- Index for user-specific queries ordered by time
CREATE INDEX IF NOT EXISTS idx_user_attempted_questions_user_time 
ON user_attempted_questions(user_id, attempted_at DESC);

-- Index for correctness-based queries
CREATE INDEX IF NOT EXISTS idx_user_attempted_questions_user_correct 
ON user_attempted_questions(user_id, is_correct);

-- Index for points-based queries
CREATE INDEX IF NOT EXISTS idx_user_attempted_questions_points 
ON user_attempted_questions(user_id, points_earned);

-- Partial index for hot questions (more efficient for hot question queries)
CREATE INDEX IF NOT EXISTS idx_questions_hot_active 
ON questions(id, hot_date) 
WHERE is_hot = true;

-- Comments for documentation
COMMENT ON FUNCTION check_user_attempted_question IS 'Efficiently checks if a user has attempted a specific question using EXISTS. O(log n) performance.';
COMMENT ON FUNCTION get_user_attempt_stats IS 'Returns comprehensive statistics for a user in a single query.';
COMMENT ON FUNCTION get_unattempted_questions IS 'Returns questions that a user has not attempted yet, with optional filtering.';
COMMENT ON FUNCTION get_user_attempt_history IS 'Returns user attempt history with question details for review purposes.';
COMMENT ON FUNCTION get_leaderboard IS 'Returns comprehensive leaderboard with attempt-based metrics.';
