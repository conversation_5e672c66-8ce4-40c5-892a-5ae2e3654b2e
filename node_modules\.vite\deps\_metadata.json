{"hash": "96bf8fb6", "configHash": "10bf2b83", "lockfileHash": "7e62d9e0", "browserHash": "de3fb56c", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "7afaca37", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "fdf2c4bc", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "0dd6910e", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "c243beea", "needsInterop": true}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "01c03a76", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "ee07b454", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "7f8da341", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "7f94ccea", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "f2724a96", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "6409ce16", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "a96ffdee", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "821999c5", "needsInterop": false}, "jwt-decode": {"src": "../../jwt-decode/build/esm/index.js", "file": "jwt-decode.js", "fileHash": "686c11d0", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "e581602c", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "051a695f", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "e346fb75", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "9e8e8056", "needsInterop": false}}, "chunks": {"browser-EMB7CRMN": {"file": "browser-EMB7CRMN.js"}, "chunk-5H4R2CZR": {"file": "chunk-5H4R2CZR.js"}, "chunk-HXVAVNLR": {"file": "chunk-HXVAVNLR.js"}, "chunk-GKJBSOWT": {"file": "chunk-GKJBSOWT.js"}, "chunk-MUDEBT3V": {"file": "chunk-MUDEBT3V.js"}, "chunk-NRBATONI": {"file": "chunk-NRBATONI.js"}, "chunk-QJTFJ6OV": {"file": "chunk-QJTFJ6OV.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}