-- Schema Migration: Transition to New User Attempted Questions System
-- This migration transitions from storing attempted questions as a UUID array in the users table
-- to using a dedicated junction table with integer question IDs

-- Step 1: Create the new user_attempted_questions table
CREATE TABLE IF NOT EXISTS user_attempted_questions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  question_id INTEGER NOT NULL, -- Will be linked to questions table after migration
  attempted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, question_id)
);

-- Step 2: Create a temporary table to store the mapping between old UUID and new integer IDs
CREATE TEMPORARY TABLE question_id_mapping (
  old_uuid_id UUID,
  new_integer_id INTEGER
);

-- Step 3: Create new questions table with integer IDs
CREATE TABLE IF NOT EXISTS questions_new (
  id SERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  category VARCHAR(50) NOT NULL CHECK (category IN ('general', 'science', 'technology', 'history', 'sports', 'entertainment')),
  difficulty VARCHAR(20) NOT NULL CHECK (difficulty IN ('easy', 'medium', 'hard')),
  options JSONB NOT NULL,
  correct_answer INTEGER NOT NULL,
  explanation TEXT,
  is_hot BOOLEAN DEFAULT FALSE,
  hot_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 4: Migrate questions data and create mapping
INSERT INTO questions_new (title, category, difficulty, options, correct_answer, explanation, is_hot, hot_date, created_at)
SELECT title, category, difficulty, options, correct_answer, explanation, is_hot, hot_date, created_at
FROM questions
ORDER BY created_at;

-- Step 5: Populate the mapping table
INSERT INTO question_id_mapping (old_uuid_id, new_integer_id)
SELECT q_old.id, q_new.id
FROM questions q_old
JOIN questions_new q_new ON (
  q_old.title = q_new.title AND 
  q_old.category = q_new.category AND 
  q_old.difficulty = q_new.difficulty AND
  q_old.correct_answer = q_new.correct_answer
);

-- Step 6: Migrate user_answers table to use new integer question IDs
CREATE TABLE IF NOT EXISTS user_answers_new (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  question_id INTEGER REFERENCES questions_new(id) ON DELETE CASCADE,
  selected_answer INTEGER NOT NULL,
  is_correct BOOLEAN NOT NULL,
  points_earned INTEGER DEFAULT 0,
  is_hot_question BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

INSERT INTO user_answers_new (id, user_id, question_id, selected_answer, is_correct, points_earned, is_hot_question, created_at)
SELECT ua.id, ua.user_id, qm.new_integer_id, ua.selected_answer, ua.is_correct, ua.points_earned, ua.is_hot_question, ua.created_at
FROM user_answers ua
JOIN question_id_mapping qm ON ua.question_id = qm.old_uuid_id;

-- Step 7: Migrate attempted_questions array to junction table
-- This handles the case where users have attempted_questions stored as UUID arrays
DO $$
DECLARE
    user_record RECORD;
    question_uuid UUID;
    question_int_id INTEGER;
BEGIN
    -- Loop through all users who have attempted_questions
    FOR user_record IN 
        SELECT id, attempted_questions 
        FROM users 
        WHERE attempted_questions IS NOT NULL AND array_length(attempted_questions, 1) > 0
    LOOP
        -- Loop through each attempted question UUID for this user
        FOREACH question_uuid IN ARRAY user_record.attempted_questions
        LOOP
            -- Find the corresponding integer ID
            SELECT new_integer_id INTO question_int_id
            FROM question_id_mapping
            WHERE old_uuid_id = question_uuid;
            
            -- Insert into junction table if mapping found
            IF question_int_id IS NOT NULL THEN
                INSERT INTO user_attempted_questions (user_id, question_id)
                VALUES (user_record.id, question_int_id)
                ON CONFLICT (user_id, question_id) DO NOTHING;
            END IF;
        END LOOP;
    END LOOP;
END $$;

-- Step 8: Also populate junction table from user_answers (in case attempted_questions array is incomplete)
INSERT INTO user_attempted_questions (user_id, question_id, attempted_at)
SELECT DISTINCT user_id, question_id, created_at
FROM user_answers_new
ON CONFLICT (user_id, question_id) DO NOTHING;

-- Step 9: Replace old tables with new ones
DROP TABLE IF EXISTS user_answers CASCADE;
DROP TABLE IF EXISTS questions CASCADE;

ALTER TABLE questions_new RENAME TO questions;
ALTER TABLE user_answers_new RENAME TO user_answers;

-- Step 10: Add foreign key constraint for user_attempted_questions
ALTER TABLE user_attempted_questions 
ADD CONSTRAINT fk_user_attempted_questions_question_id 
FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE;

-- Step 11: Remove attempted_questions column from users table
ALTER TABLE users DROP COLUMN IF EXISTS attempted_questions;

-- Step 12: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_questions_category ON questions(category);
CREATE INDEX IF NOT EXISTS idx_questions_difficulty ON questions(difficulty);
CREATE INDEX IF NOT EXISTS idx_questions_hot ON questions(is_hot, hot_date);
CREATE INDEX IF NOT EXISTS idx_user_answers_user_id ON user_answers(user_id);
CREATE INDEX IF NOT EXISTS idx_user_answers_question_id ON user_answers(question_id);
CREATE INDEX IF NOT EXISTS idx_user_attempted_questions_user_id ON user_attempted_questions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_attempted_questions_question_id ON user_attempted_questions(question_id);

-- Step 13: Clean up temporary table
DROP TABLE question_id_mapping;

-- Verification queries (run these to verify the migration worked correctly)
-- SELECT COUNT(*) as total_questions FROM questions;
-- SELECT COUNT(*) as total_user_answers FROM user_answers;
-- SELECT COUNT(*) as total_attempted_questions FROM user_attempted_questions;
-- SELECT COUNT(*) as users_with_attempted_questions FROM (SELECT DISTINCT user_id FROM user_attempted_questions) t;
